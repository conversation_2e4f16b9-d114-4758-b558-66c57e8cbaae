using Microsoft.Azure.Functions.Worker;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using ScoreboardCsvIngestor.Configuration;
using ScoreboardCsvIngestor.Data;
using ScoreboardCsvIngestor.Services;

var host = new HostBuilder()
    .ConfigureFunctionsWebApplication()
    .ConfigureServices((context, services) =>
    {
        // Get app settings
        var appSettings = context.Configuration.GetAppSettings();
        services.AddSingleton(appSettings);

        // Add Application Insights
        if (!string.IsNullOrEmpty(appSettings.ApplicationInsightsConnectionString))
        {
            services.AddApplicationInsightsTelemetryWorkerService();
            services.ConfigureFunctionsApplicationInsights();
        }

        // Add Entity Framework
        services.AddDbContext<ScoreboardDbContext>(options =>
        {
            options.UseSqlServer(appSettings.SqlConnectionString);
            if (appSettings.EnableDetailedLogging)
            {
                options.EnableSensitiveDataLogging();
                options.EnableDetailedErrors();
            }
        });

        // Add custom services
        services.AddScoped<ICsvParserService, CsvParserService>();
        services.AddScoped<IScoreboardDataService, ScoreboardDataService>();

        // Configure logging
        services.AddLogging(builder =>
        {
            builder.AddConsole();
            if (!string.IsNullOrEmpty(appSettings.ApplicationInsightsConnectionString))
            {
                builder.AddApplicationInsights();
            }

            if (appSettings.EnableDetailedLogging)
            {
                builder.SetMinimumLevel(LogLevel.Debug);
            }
        });
    })
    .Build();

host.Run();
