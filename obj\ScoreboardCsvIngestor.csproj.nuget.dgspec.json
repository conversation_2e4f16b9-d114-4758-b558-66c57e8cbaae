{"format": 1, "restore": {"C:\\Users\\<USER>\\Documents\\augment-projects\\scoreboard-csv-ingestor\\ScoreboardCsvIngestor.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Documents\\augment-projects\\scoreboard-csv-ingestor\\ScoreboardCsvIngestor.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Documents\\augment-projects\\scoreboard-csv-ingestor\\ScoreboardCsvIngestor.csproj", "projectName": "ScoreboardCsvIngestor", "projectPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\scoreboard-csv-ingestor\\ScoreboardCsvIngestor.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Documents\\augment-projects\\scoreboard-csv-ingestor\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.telerik.com/nuget": {}, "https://pfsbrands.pkgs.visualstudio.com/_packaging/PFSbrandsNuGet/nuget/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"CsvHelper": {"target": "Package", "version": "[32.0.3, )"}, "Microsoft.ApplicationInsights.WorkerService": {"target": "Package", "version": "[2.22.0, )"}, "Microsoft.Azure.Functions.Worker": {"target": "Package", "version": "[1.21.0, )"}, "Microsoft.Azure.Functions.Worker.ApplicationInsights": {"target": "Package", "version": "[1.2.0, )"}, "Microsoft.Azure.Functions.Worker.Extensions.Http": {"target": "Package", "version": "[3.1.0, )"}, "Microsoft.Azure.Functions.Worker.Sdk": {"target": "Package", "version": "[1.16.4, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.4, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.4, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}