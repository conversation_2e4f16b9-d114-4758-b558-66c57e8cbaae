# Azure Function Deployment Script
# This script deploys the Scoreboard CSV Ingestor to Azure Functions

param(
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory=$false)]
    [string]$ResourceGroupName = "rg-scoreboard-functions",
    
    [Parameter(Mandatory=$false)]
    [string]$Location = "East US",
    
    [Parameter(Mandatory=$false)]
    [string]$StorageAccountName = "stscoreboardfunc$(Get-Random -Minimum 1000 -Maximum 9999)"
)

Write-Host "🚀 Starting Azure Function deployment..." -ForegroundColor Green

# Check if Azure CLI is installed
if (!(Get-Command az -ErrorAction SilentlyContinue)) {
    Write-Error "Azure CLI is not installed. Please install it from https://docs.microsoft.com/en-us/cli/azure/install-azure-cli"
    exit 1
}

# Check if Functions Core Tools is installed
if (!(Get-Command func -ErrorAction SilentlyContinue)) {
    Write-Error "Azure Functions Core Tools is not installed. Please install it with: npm install -g azure-functions-core-tools@4 --unsafe-perm true"
    exit 1
}

# Login to Azure (if not already logged in)
Write-Host "🔐 Checking Azure login status..." -ForegroundColor Yellow
$loginStatus = az account show 2>$null
if (!$loginStatus) {
    Write-Host "Please login to Azure..." -ForegroundColor Yellow
    az login
}

# Create resource group if it doesn't exist
Write-Host "📦 Creating resource group: $ResourceGroupName" -ForegroundColor Yellow
az group create --name $ResourceGroupName --location $Location

# Create storage account
Write-Host "💾 Creating storage account: $StorageAccountName" -ForegroundColor Yellow
az storage account create --name $StorageAccountName --location $Location --resource-group $ResourceGroupName --sku Standard_LRS

# Create Function App
Write-Host "⚡ Creating Function App: $FunctionAppName" -ForegroundColor Yellow
az functionapp create --resource-group $ResourceGroupName --consumption-plan-location $Location --runtime dotnet-isolated --runtime-version 8.0 --functions-version 4 --name $FunctionAppName --storage-account $StorageAccountName

# Configure Application Settings
Write-Host "⚙️ Configuring application settings..." -ForegroundColor Yellow

# Read connection string from local.settings.json
$localSettings = Get-Content "local.settings.json" | ConvertFrom-Json
$sqlConnectionString = $localSettings.Values.SqlConnectionString
$maxCsvRows = $localSettings.Values.MaxCsvRows
$enableDetailedLogging = "false"  # Set to false for production

# Set application settings
az functionapp config appsettings set --name $FunctionAppName --resource-group $ResourceGroupName --settings @(
    "SqlConnectionString=$sqlConnectionString",
    "MaxCsvRows=$maxCsvRows",
    "EnableDetailedLogging=$enableDetailedLogging",
    "FUNCTIONS_WORKER_RUNTIME=dotnet-isolated"
)

# Build the project
Write-Host "🔨 Building the project..." -ForegroundColor Yellow
dotnet build --configuration Release

# Deploy the function
Write-Host "🚀 Deploying to Azure Functions..." -ForegroundColor Yellow
func azure functionapp publish $FunctionAppName

Write-Host "✅ Deployment completed successfully!" -ForegroundColor Green
Write-Host "🌐 Function App URL: https://$FunctionAppName.azurewebsites.net" -ForegroundColor Cyan
Write-Host "📋 Health Check: https://$FunctionAppName.azurewebsites.net/api/csv/health" -ForegroundColor Cyan
Write-Host "📤 CSV Upload: https://$FunctionAppName.azurewebsites.net/api/csv/upload" -ForegroundColor Cyan

# Get function key for Logic Apps
Write-Host "🔑 Getting function key for Logic Apps..." -ForegroundColor Yellow
$functionKey = az functionapp keys list --name $FunctionAppName --resource-group $ResourceGroupName --query "functionKeys.default" -o tsv

Write-Host "🔐 Function Key (save this for Logic Apps): $functionKey" -ForegroundColor Magenta
Write-Host "💡 Use this key in Logic Apps HTTP action header: x-functions-key: $functionKey" -ForegroundColor Yellow
