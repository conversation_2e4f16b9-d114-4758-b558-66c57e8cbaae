# 🚀 Azure Function & Logic App Deployment Guide

This guide will help you deploy the Scoreboard CSV Ingestor as an Azure Function and set up Logic App integration.

## 📋 Prerequisites

1. **Azure CLI** - [Install Azure CLI](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
2. **Azure Functions Core Tools** - Install with:
   ```bash
   npm install -g azure-functions-core-tools@4 --unsafe-perm true
   ```
3. **Azure Subscription** with permissions to create resources
4. **PowerShell** (for Windows) or **Bash** (for Linux/Mac)

## 🎯 Step 1: Deploy Azure Function

### Option A: Automated Deployment (Recommended)

1. **Run the deployment script:**
   ```powershell
   .\deploy-to-azure.ps1 -FunctionAppName "your-scoreboard-function-app"
   ```

2. **Follow the prompts:**
   - Login to Azure when prompted
   - Wait for resources to be created
   - Note the function key displayed at the end

### Option B: Manual Deployment

1. **Login to Azure:**
   ```bash
   az login
   ```

2. **Create Resource Group:**
   ```bash
   az group create --name rg-scoreboard-functions --location "East US"
   ```

3. **Create Storage Account:**
   ```bash
   az storage account create --name stscoreboardfunc1234 --location "East US" --resource-group rg-scoreboard-functions --sku Standard_LRS
   ```

4. **Create Function App:**
   ```bash
   az functionapp create --resource-group rg-scoreboard-functions --consumption-plan-location "East US" --runtime dotnet-isolated --runtime-version 8.0 --functions-version 4 --name your-scoreboard-function-app --storage-account stscoreboardfunc1234
   ```

5. **Configure App Settings:**
   ```bash
   az functionapp config appsettings set --name your-scoreboard-function-app --resource-group rg-scoreboard-functions --settings "SqlConnectionString=Server=tcp:alpaca-sql-dev.database.windows.net,1433;Initial Catalog=proof-of-concept;Persist Security Info=False;User ID=pfsadmin;Password=*********;MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;" "MaxCsvRows=500" "EnableDetailedLogging=false"
   ```

6. **Deploy Function:**
   ```bash
   func azure functionapp publish your-scoreboard-function-app
   ```

## 🔗 Step 2: Set Up Logic App

### Create Logic App

1. **Go to Azure Portal** → Create Resource → Logic App
2. **Choose Consumption plan** for pay-per-use
3. **Create in the same resource group** as your Function App

### Configure Logic App Workflow

1. **Open Logic App Designer**
2. **Import the template:**
   - Copy content from `logic-app-template.json`
   - Paste in Code View
   - Update parameters:
     - `functionAppName`: Your Function App name
     - `functionKey`: Function key from deployment
     - `storageAccountConnection`: Your storage connection string

3. **Set up connections:**
   - **Azure Blob Storage**: Connect to your storage account
   - **Create folders**: `/csvinput`, `/processed`, `/error`

### Alternative: Manual Logic App Setup

1. **Add Blob Trigger:**
   - When a blob is added or modified
   - Container: `csvinput`
   - Check every 1 minute

2. **Add HTTP Action:**
   - Method: POST
   - URL: `https://your-function-app.azurewebsites.net/api/csv/upload`
   - Headers:
     ```
     Content-Type: text/csv
     x-functions-key: [YOUR-FUNCTION-KEY]
     ```
   - Body: `@body('Get_blob_content')`

3. **Add Condition:**
   - If status code equals 200: Success actions
   - Else: Error handling actions

## 🧪 Step 3: Test the Integration

### Test Function Directly

1. **Health Check:**
   ```bash
   curl https://your-function-app.azurewebsites.net/api/csv/health
   ```

2. **CSV Upload Test:**
   ```bash
   curl -X POST https://your-function-app.azurewebsites.net/api/csv/upload \
     -H "Content-Type: text/csv" \
     -H "x-functions-key: YOUR-FUNCTION-KEY" \
     --data-binary @sample-data.csv
   ```

### Test Logic App

1. **Upload CSV file** to the `/csvinput` container
2. **Check Logic App run history** in Azure Portal
3. **Verify data** in your SQL database
4. **Check processed/error folders** for file movement

## 📊 Step 4: Monitoring & Troubleshooting

### Application Insights

1. **Enable Application Insights** on your Function App
2. **Monitor performance** and errors
3. **Set up alerts** for failures

### Common Issues

1. **Database Connection Errors:**
   - Verify connection string in App Settings
   - Check firewall rules on Azure SQL
   - Ensure password is correct

2. **CSV Parsing Errors:**
   - Verify CSV format matches expected structure
   - Check date format: `dd/MM/yyyy h:mm tt`
   - Ensure 8 columns with correct headers

3. **Logic App Failures:**
   - Check function key is correct
   - Verify blob storage permissions
   - Review Logic App run history

### Logs and Debugging

1. **Function Logs:**
   ```bash
   func azure functionapp logstream your-function-app
   ```

2. **Application Insights Queries:**
   ```kusto
   traces
   | where timestamp > ago(1h)
   | where severityLevel >= 2
   | order by timestamp desc
   ```

## 🔒 Security Best Practices

1. **Use Key Vault** for sensitive connection strings
2. **Enable Managed Identity** for Azure-to-Azure communication
3. **Restrict Function App** to Logic Apps only
4. **Use HTTPS only** for all communications
5. **Regular key rotation** for function keys

## 📈 Performance Optimization

1. **Monitor processing times** in Application Insights
2. **Adjust batch sizes** if needed (currently 100 records)
3. **Scale Function App** based on load
4. **Optimize SQL queries** if performance issues arise

## 🎉 Success Indicators

✅ Function App deploys successfully  
✅ Health check returns 200 OK  
✅ CSV upload processes without errors  
✅ Data appears in SQL database  
✅ Logic App runs complete successfully  
✅ Files move to processed folder  

Your Scoreboard CSV Ingestor is now live and ready for production use! 🚀
