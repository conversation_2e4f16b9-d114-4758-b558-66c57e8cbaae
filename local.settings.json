{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "UseDevelopmentStorage=true", "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated", "APPLICATIONINSIGHTS_CONNECTION_STRING": "", "SqlConnectionString": "Server=tcp:alpaca-sql-dev.database.windows.net,1433;Initial Catalog=proof-of-concept;Persist Security Info=False;User ID=pfsadmin;Password={your_password};MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;", "MaxCsvRows": "500", "EnableDetailedLogging": "true"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}