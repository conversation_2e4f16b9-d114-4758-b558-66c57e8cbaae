{"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"functionAppName": {"type": "string", "defaultValue": "[YOUR-FUNCTION-APP-NAME]", "metadata": {"description": "Name of your Azure Function App"}}, "functionKey": {"type": "securestring", "metadata": {"description": "Function key for authentication"}}, "storageAccountConnection": {"type": "string", "defaultValue": "[STORAGE-CONNECTION-STRING]", "metadata": {"description": "Storage account connection string for blob trigger"}}}, "triggers": {"When_a_blob_is_added_or_modified": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "get", "path": "/datasets/default/triggers/batch/onupdatedfile", "queries": {"folderId": "L2NzdmlucHV0", "maxFileCount": 1}}, "recurrence": {"frequency": "Minute", "interval": 1}, "splitOn": "@triggerBody()", "metadata": {"L2NzdmlucHV0": "/csvinput"}}}, "actions": {"Get_blob_content": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "get", "path": "/datasets/default/files/@{encodeURIComponent(encodeURIComponent(triggerBody()?['Path']))}/content", "queries": {"inferContentType": true}}, "runAfter": {}}, "Call_CSV_Processor_Function": {"type": "Http", "inputs": {"method": "POST", "uri": "https://@{parameters('functionAppName')}.azurewebsites.net/api/csv/upload", "headers": {"Content-Type": "text/csv", "x-functions-key": "@parameters('functionKey')"}, "body": "@body('Get_blob_content')"}, "runAfter": {"Get_blob_content": ["Succeeded"]}}, "Check_Processing_Result": {"type": "If", "expression": {"and": [{"equals": ["@outputs('Call_CSV_Processor_Function')['statusCode']", 200]}]}, "actions": {"Success_Notification": {"type": "Compose", "inputs": {"status": "Success", "message": "CSV file processed successfully", "fileName": "@triggerBody()?['Name']", "recordsInserted": "@body('Call_CSV_Processor_Function')?['data']?['recordsInserted']", "totalRows": "@body('Call_CSV_Processor_Function')?['data']?['totalRowsProcessed']", "processingTime": "@body('Call_CSV_Processor_Function')?['data']?['processingTimeMs']", "correlationId": "@body('Call_CSV_Processor_Function')?['correlationId']", "timestamp": "@utcNow()"}}, "Move_to_Processed_Folder": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "post", "path": "/datasets/default/copyFile", "queries": {"source": "@triggerBody()?['Path']", "destination": "/processed/@{triggerBody()?['Name']}"}}, "runAfter": {"Success_Notification": ["Succeeded"]}}, "Delete_Original_File": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "delete", "path": "/datasets/default/files/@{encodeURIComponent(encodeURIComponent(triggerBody()?['Path']))}"}, "runAfter": {"Move_to_Processed_Folder": ["Succeeded"]}}}, "else": {"actions": {"Error_Notification": {"type": "Compose", "inputs": {"status": "Error", "message": "CSV file processing failed", "fileName": "@triggerBody()?['Name']", "errorMessage": "@body('Call_CSV_Processor_Function')?['message']", "errors": "@body('Call_CSV_Processor_Function')?['errors']", "validationErrors": "@body('Call_CSV_Processor_Function')?['validationErrors']", "correlationId": "@body('Call_CSV_Processor_Function')?['correlationId']", "statusCode": "@outputs('Call_CSV_Processor_Function')['statusCode']", "timestamp": "@utcNow()"}}, "Move_to_Error_Folder": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['azureblob']['connectionId']"}}, "method": "post", "path": "/datasets/default/copyFile", "queries": {"source": "@triggerBody()?['Path']", "destination": "/error/@{triggerBody()?['Name']}"}}, "runAfter": {"Error_Notification": ["Succeeded"]}}}}, "runAfter": {"Call_CSV_Processor_Function": ["Succeeded", "Failed"]}}}, "outputs": {}}