using Microsoft.EntityFrameworkCore;
using ScoreboardCsvIngestor.Models;

namespace ScoreboardCsvIngestor.Data;

public class ScoreboardDbContext : DbContext
{
    public ScoreboardDbContext(DbContextOptions<ScoreboardDbContext> options) : base(options)
    {
    }

    public DbSet<ScoreboardRecord> ScoreboardRecords { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<ScoreboardRecord>(entity =>
        {
            entity.HasKey(e => e.Id);
            
            entity.Property(e => e.Id)
                .ValueGeneratedOnAdd();

            entity.Property(e => e.StoreName)
                .IsRequired()
                .HasMaxLength(255);

            entity.Property(e => e.LaneDepartureTime)
                .IsRequired()
                .HasColumnType("datetime2");

            entity.Property(e => e.CarType)
                .IsRequired()
                .HasMaxLength(100);

            entity.Property(e => e.LaneNumber)
                .IsRequired();

            entity.Property(e => e.OrderTime)
                .IsRequired();

            entity.Property(e => e.PickupTime)
                .IsRequired();

            entity.Property(e => e.QueueTime)
                .IsRequired();

            entity.Property(e => e.TotalTime)
                .IsRequired();

            entity.Property(e => e.CreatedAt)
                .IsRequired()
                .HasColumnType("datetime2")
                .HasDefaultValueSql("GETUTCDATE()");

            entity.Property(e => e.ProcessedAt)
                .IsRequired()
                .HasColumnType("datetime2")
                .HasDefaultValueSql("GETUTCDATE()");

            // Add indexes for common queries
            entity.HasIndex(e => e.StoreName)
                .HasDatabaseName("IX_ScoreboardRecords_StoreName");

            entity.HasIndex(e => e.LaneDepartureTime)
                .HasDatabaseName("IX_ScoreboardRecords_LaneDepartureTime");

            entity.HasIndex(e => e.CreatedAt)
                .HasDatabaseName("IX_ScoreboardRecords_CreatedAt");
        });
    }
}
