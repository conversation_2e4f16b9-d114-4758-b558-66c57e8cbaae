using Microsoft.EntityFrameworkCore;
using ScoreboardCsvIngestor.Models;

namespace ScoreboardCsvIngestor.Data;

public class ScoreboardDbContext : DbContext
{
    public ScoreboardDbContext(DbContextOptions<ScoreboardDbContext> options) : base(options)
    {
    }

    public DbSet<ScoreboardRecord> EveryCarChamps { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Entity<ScoreboardRecord>(entity =>
        {
            entity.ToTable("EveryCarChamps");

            // Use composite key since there's no identity column
            entity.HasKey(e => new { e.StoreName, e.LaneDepartureTime, e.Lane<PERSON>umber });

            entity.Property(e => e.StoreName)
                .IsRequired()
                .HasMaxLength(100)
                .HasColumnType("VARCHAR(100)");

            entity.Property(e => e.LaneDepartureTime)
                .IsRequired()
                .HasColumnType("DATETIME");

            entity.Property(e => e.CarType)
                .IsRequired()
                .HasMaxLength(50)
                .HasColumnType("VARCHAR(50)");

            entity.Property(e => e.LaneNumber)
                .IsRequired()
                .HasColumnType("INT");

            entity.Property(e => e.OrderTime)
                .IsRequired()
                .HasColumnType("INT");

            entity.Property(e => e.PickupTime)
                .IsRequired()
                .HasColumnType("INT");

            entity.Property(e => e.QueueTime)
                .IsRequired()
                .HasColumnType("INT");

            entity.Property(e => e.TotalTime)
                .IsRequired()
                .HasColumnType("INT");
        });
    }
}
