# Test Azure Function Deployment
# This script tests the deployed Azure Function

param(
    [Parameter(Mandatory=$true)]
    [string]$FunctionAppName,
    
    [Parameter(Mandatory=$false)]
    [string]$FunctionKey = "",
    
    [Parameter(Mandatory=$false)]
    [string]$CsvFile = "sample-data.csv"
)

$baseUrl = "https://$FunctionAppName.azurewebsites.net"

Write-Host "🧪 Testing Azure Function deployment..." -ForegroundColor Green
Write-Host "🌐 Function App: $baseUrl" -ForegroundColor Cyan

# Test 1: Health Check
Write-Host "`n1️⃣ Testing Health Check..." -ForegroundColor Yellow
try {
    $healthResponse = Invoke-RestMethod -Uri "$baseUrl/api/csv/health" -Method GET
    if ($healthResponse.success) {
        Write-Host "✅ Health Check: PASSED" -ForegroundColor Green
        Write-Host "   Database Status: $($healthResponse.data.checks.database)" -ForegroundColor Cyan
    } else {
        Write-Host "❌ Health Check: FAILED" -ForegroundColor Red
        Write-Host "   Error: $($healthResponse.message)" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ Health Check: ERROR" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 2: CSV Upload (if function key provided)
if ($FunctionKey -and (Test-Path $CsvFile)) {
    Write-Host "`n2️⃣ Testing CSV Upload..." -ForegroundColor Yellow
    try {
        $csvContent = Get-Content $CsvFile -Raw
        $headers = @{
            'Content-Type' = 'text/csv'
            'x-functions-key' = $FunctionKey
        }
        
        $uploadResponse = Invoke-RestMethod -Uri "$baseUrl/api/csv/upload" -Method POST -Body $csvContent -Headers $headers
        
        if ($uploadResponse.success) {
            Write-Host "✅ CSV Upload: PASSED" -ForegroundColor Green
            Write-Host "   Records Inserted: $($uploadResponse.data.recordsInserted)" -ForegroundColor Cyan
            Write-Host "   Total Rows: $($uploadResponse.data.totalRowsProcessed)" -ForegroundColor Cyan
            Write-Host "   Processing Time: $($uploadResponse.data.processingTimeMs)ms" -ForegroundColor Cyan
            Write-Host "   Correlation ID: $($uploadResponse.correlationId)" -ForegroundColor Cyan
        } else {
            Write-Host "❌ CSV Upload: FAILED" -ForegroundColor Red
            Write-Host "   Error: $($uploadResponse.message)" -ForegroundColor Red
            if ($uploadResponse.errors) {
                Write-Host "   Errors: $($uploadResponse.errors -join ', ')" -ForegroundColor Red
            }
        }
    } catch {
        Write-Host "❌ CSV Upload: ERROR" -ForegroundColor Red
        Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    }
} elseif (!$FunctionKey) {
    Write-Host "`n2️⃣ Skipping CSV Upload test (no function key provided)" -ForegroundColor Yellow
    Write-Host "   To test CSV upload, provide -FunctionKey parameter" -ForegroundColor Gray
} elseif (!(Test-Path $CsvFile)) {
    Write-Host "`n2️⃣ Skipping CSV Upload test (CSV file not found: $CsvFile)" -ForegroundColor Yellow
    Write-Host "   To test CSV upload, ensure sample-data.csv exists" -ForegroundColor Gray
}

# Test 3: Function App Configuration
Write-Host "`n3️⃣ Checking Function App Configuration..." -ForegroundColor Yellow
try {
    # This requires Azure CLI and appropriate permissions
    $appSettings = az functionapp config appsettings list --name $FunctionAppName --resource-group rg-scoreboard-functions --query "[?name=='SqlConnectionString' || name=='MaxCsvRows' || name=='EnableDetailedLogging']" | ConvertFrom-Json
    
    if ($appSettings) {
        Write-Host "✅ Configuration Check: PASSED" -ForegroundColor Green
        foreach ($setting in $appSettings) {
            if ($setting.name -eq "SqlConnectionString") {
                Write-Host "   SQL Connection: Configured" -ForegroundColor Cyan
            } else {
                Write-Host "   $($setting.name): $($setting.value)" -ForegroundColor Cyan
            }
        }
    } else {
        Write-Host "❌ Configuration Check: No settings found" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠️ Configuration Check: Skipped (requires Azure CLI)" -ForegroundColor Yellow
}

Write-Host "`n🎉 Testing completed!" -ForegroundColor Green
Write-Host "`n📋 Next Steps:" -ForegroundColor Cyan
Write-Host "   1. If health check passed, your function is ready!" -ForegroundColor White
Write-Host "   2. Set up Logic App using the template in logic-app-template.json" -ForegroundColor White
Write-Host "   3. Create blob storage containers: csvinput, processed, error" -ForegroundColor White
Write-Host "   4. Upload CSV files to csvinput container to trigger processing" -ForegroundColor White

if (!$FunctionKey) {
    Write-Host "`n🔑 To get your function key:" -ForegroundColor Yellow
    Write-Host "   az functionapp keys list --name $FunctionAppName --resource-group rg-scoreboard-functions --query `"functionKeys.default`" -o tsv" -ForegroundColor Gray
}
